import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import '../../pages/community/switch_bind_role.dart';
import '../webview_bridge_interface.dart';

class ShowGameBindingDialogHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "showGameBindingDialog";

  ShowGameBindingDialogHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) {
    String callback = message.data["callback"] ?? '';
    String dialogType =
        message.data["dialogType"] ?? GameBindingDialogType.DEFAULT.name;
    final isNewGift = dialogType == GameBindingDialogType.NEW_GIFT.name;
    LogUtil.d("showGameBindingDialog: $dialogType, callback = $callback, isNewGift = $isNewGift");
    SwitchBindRoleDialog.showCharacterSwitchDialog(
      context,
      onCharacterSwitched: () {},
      onNavigateToBindCharacter: () {},
      onClose: () {
        if (callback.isNotEmpty) {
          final selectRole = SwitchBindRoleDialog.getSelectedRole(context);
          executeJsMethod(callback, {'selectRole': selectRole});
        }
      },
      isNewGift: isNewGift,
    );
  }
}

enum GameBindingDialogType {
  DEFAULT, //默认类型
  NEW_GIFT, //新手礼包类型
}

import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:dlyz_flutter/webview/webview_bridge_interface.dart';
import '../../utils/log_util.dart';
import '../../utils/permission_utils.dart';

class RequestPermissionHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "requestPermission";

  RequestPermissionHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    String permission = message.data["permissionName"] ?? '';
    PermissionType? permissionType;
    if ('notification' == permission) {
      permissionType = PermissionType.notification;
    }
    if (permissionType == null) return;
    final isGranted = await PermissionUtils.requestPermission(context, permissionType);
    String callback = message.data["callback"] ?? '';
    LogUtil.d("requestPermission: $isGranted, callback = $callback");
    if (callback.isNotEmpty) {
      executeJsMethod(callback, {'isGranted': isGranted});
    }
  }
}

import 'package:dlyz_flutter/zhifu/zhifu_strategy_interface.dart';
import '../../manager/channel_manager.dart';
import '../zhifu_type.dart';

class AndroidWechatZhiFuStrategy implements IZhiFuStrategy {
  @override
  Future<Map<String, String>> zhiFu(Map<String, dynamic> params) async {
    ///暂时和ios保持一致，后续迭代优化
    String tradeInfo = params['trade'] ?? '';
    await ChannelManager().openUrl(url: tradeInfo);
    return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_UNKNOWN, '未知');
  }
}

import '../../manager/channel_manager.dart';
import '../../utils/log_util.dart';
import '../zhifu_strategy_interface.dart';
import '../zhifu_type.dart';

class IosWechatZhiFuStrategy implements IZhiFuStrategy {
  @override
  Future<Map<String, String>> zhiFu(Map<String, dynamic> params) async {
    String tradeInfo = params['trade'] ?? '';
    LogUtil.d("tradeInfo: $tradeInfo");
    await ChannelManager().openUrl(url: tradeInfo);
    return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_UNKNOWN, '未知');
  }

}

import 'dart:async';

/// 全局事件总线
class EventBus {
  static final EventBus _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();

  final Map<Type, StreamController> _controllers = {};

  /// 发送事件
  void emit<T>(T event) {
    final controller = _controllers[T] as StreamController<T>?;
    controller?.add(event);
  }

  /// 监听事件
  Stream<T> on<T>() {
    final controller = _controllers[T] as StreamController<T>?;
    if (controller != null) {
      return controller.stream;
    }
    
    final newController = StreamController<T>.broadcast();
    _controllers[T] = newController;
    return newController.stream;
  }

  /// 销毁
  void dispose() {
    for (final controller in _controllers.values) {
      controller.close();
    }
    _controllers.clear();
  }
}

/// 登录失效事件
class LoginExpiredEvent {
  final String message;

  LoginExpiredEvent({
    required this.message,
  });
}

/// 全局事件总线实例
final eventBus = EventBus();
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../providers/user_provider.dart';
import '../../model/user.dart';
import '../login/unified_login_page.dart';
import '../../services/app_route_manager.dart';
import '../../components/common_alert.dart';

class AccountManagementPage extends StatefulWidget {
  final bool welcomeMode;
  const AccountManagementPage({super.key, this.welcomeMode = false});

  @override
  State<AccountManagementPage> createState() => _AccountManagementPageState();
}

class _AccountManagementPageState extends State<AccountManagementPage> {
  String? _switchingUserId;

  @override
  void initState() {
    super.initState();
  }

  String _getAccountInfo(User user) {
    String loginType = user.loginType;
    String accountTypeText;
    String account;
    switch (loginType) {
      case 'phone':
        accountTypeText = '手机账号';
        account = user.loginInfo.recordPhone ?? user.userDetail.loginPhone ?? user.loginInfo.muname ?? '';
        break;
      case 'wechat':
        accountTypeText = '微信账号';
        account = user.loginInfo.muname ?? '';
        break;
      case 'common':
        accountTypeText = '账密账号';
        account = user.loginInfo.muname ?? '';
        break;
      default:
        accountTypeText = '账号';
        account = user.loginInfo.muname ?? '';
        break;
    }
    return '$accountTypeText $account';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: !widget.welcomeMode,
        leading: widget.welcomeMode ? const SizedBox.shrink() : IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '账号管理',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Consumer<UserProvider>(
        builder: (context, userProvider, child) {
          final currentUser = userProvider.currentUser;
          final userList = userProvider.userList;
          
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 20),
                      if (widget.welcomeMode) ...[
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            '登录后可领取更多游戏福利和内容',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF303133),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                      if (userList.isNotEmpty) 
                        ...userList.asMap().entries.map((entry) {
                          int index = entry.key;
                          User user = entry.value;
                          return _buildAccountItem(user, index, currentUser);
                        })
                      else
                        const Padding(
                          padding: EdgeInsets.all(20.0),
                          child: Text(
                            '暂无登录记录',
                            style: TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      const SizedBox(height: 16),
                      _buildAddAccountButton(),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
              if (!widget.welcomeMode) ...[
                _buildLogoutButton(),
                const SizedBox(height: 34),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildAccountItem(User user, int index, User? currentUser) {
    bool isCurrentUser = currentUser != null && user.muid == currentUser.muid;
    // 在welcome模式下，第一个item显示为最近登录
    bool isRecentUser = widget.welcomeMode && index == 0;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F7F9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () async {
          // welcome模式下点击任何用户都进行切换（相当于登录）
          if (widget.welcomeMode || (!isCurrentUser && _switchingUserId == null)) {
            setState(() {
              _switchingUserId = user.muid;
            });
            
            try {
              final userProvider = Provider.of<UserProvider>(context, listen: false);
              final result = await userProvider.switchUser(user);
              final success = result.$1;
              if (success) {
                if (widget.welcomeMode) {
                  // welcome模式下登录成功，进入登录成功的后续流程
                  if (mounted) {
                    AppRouteManager.navigateAfterLogin(context);
                  }
                } else {
                  // 普通模式下显示切换成功提示
                  Fluttertoast.showToast(
                    msg: '账号切换成功',
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.CENTER,
                  );
                }
              }
            } catch (e) {
              // 异常处理
              Fluttertoast.showToast(
                msg: '切换失败: $e',
                toastLength: Toast.LENGTH_LONG,
                gravity: ToastGravity.CENTER,
              );
            } finally {
              if (mounted) {
                setState(() {
                  _switchingUserId = null;
                });
              }
            }
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: const Color(0xFFE6F3FF),
                child: user.userDetail.avatar != null && user.userDetail.avatar!.isNotEmpty
                    ? ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: user.userDetail.avatar!,
                          width: 48,
                          height: 48,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 48,
                            height: 48,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [Color(0xFF4A90E2), Color(0xFF7BB3F0)],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 48,
                            height: 48,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                colors: [Color(0xFF4A90E2), Color(0xFF7BB3F0)],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      )
                    : Container(
                        width: 48,
                        height: 48,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [Color(0xFF4A90E2), Color(0xFF7BB3F0)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: const Icon(
                          Icons.person,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.alias,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getAccountInfo(user),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF999999),
                      ),
                    ),
                  ],
                ),
              ),
              // welcome模式下隐藏切换tag，或者显示最近登录tag
              if (!widget.welcomeMode)
                Container(
                  width: 60,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCurrentUser 
                        ? const Color(0x1A1D6FE9)
                        : const Color(0xFF4571FB),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  alignment: Alignment.center,
                  child: _switchingUserId == user.muid
                      ? const SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          isCurrentUser ? '当前登录' : '切换',
                          style: TextStyle(
                            color: isCurrentUser ? const Color(0xFF1D6FE9) : Colors.white,
                            fontSize: isCurrentUser ? 12: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                )
              else if (isRecentUser)
                Container(
                  width: 60,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0x1A1D6FE9),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  alignment: Alignment.center,
                  child: _switchingUserId == user.muid
                      ? const SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Color(0xFF1D6FE9),
                          ),
                        )
                      : const Text(
                          '最近登录',
                          style: TextStyle(
                            color: Color(0xFF1D6FE9),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddAccountButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: TextButton(
        onPressed: () async {
          // 跳转到登录页面
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const UnifiedLoginPage(
                directPop: true,
              ),
            ),
          );
          
          // 登录成功后数据会通过Consumer自动更新
          // 不需要手动刷新
        },
        style: TextButton.styleFrom(
          backgroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: const BorderSide(
              color: Color(0xFFE0E0E0),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Icon(
              Icons.add,
              color: Color(0xFF666666),
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              '添加账号',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: TextButton(
        onPressed: () async {
          // 显示确认对话框
          CommonAlert.show(
            context: context,
            title: '确认退出',
            content: '确定要退出当前账号吗？',
            onConfirm: () {
              // 使用统一的退出登录方法
              AppRouteManager.navigateAfterLogout(context);
            },
          );
        },
        style: TextButton.styleFrom(
          backgroundColor: Color(0xFFF6F7F9),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '退出登录',
          style: TextStyle(
            fontSize: 16,
            color: Color(0xFF007AFF),
          ),
        ),
      ),
    );
  }

}
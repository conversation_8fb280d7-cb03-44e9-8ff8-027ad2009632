import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../components/common_alert.dart';
import '../../providers/user_provider.dart';
import '../../pages/settings/AccountManagementPage.dart';
import '../../pages/login/unified_login_page.dart';
import '../../services/app_route_manager.dart';
import '../../utils/event_bus.dart';

class LoginValidationInterceptor extends Interceptor {
  static const int invalidTokenCode = 85004;
  static const int loginExpiredCode = 85005;


  LoginValidationInterceptor();

  bool _isGetOrPost(RequestOptions o) {
    final m = o.method.toUpperCase();
    return m == 'GET' || m == 'POST';
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    final opts = response.requestOptions;

    try {
      // 跳过票据刷新接口的登录验证，让它自己处理
      if (opts.path.contains('/api/gamehub-api/v1/login/ticket/refresh')) {
        handler.next(response);
        return;
      }

      final int? code = _tryReadCode(response.data);


      // 检查是否是需要处理的登录失效状态码
      if ((code != invalidTokenCode && code != loginExpiredCode) || !_isGetOrPost(opts) ) {
        handler.next(response);
        return;
      }

      String message = '';
      if (code == invalidTokenCode) {
        message = '您的账号已下线，请重新登录';
      } else if (code == loginExpiredCode) {
        message = '您的账号已在其他设备登录';
      }
      await _onLoginExpired(message);

      handler.next(response);
    } catch (e) {
      handler.next(response); // 兜底不拦截
    } finally {
    }
  }

  int? _tryReadCode(dynamic data) {
    try {
      Map<String, dynamic>? jsonMap;
      
      // 处理不同类型的数据
      if (data is String) {
        // 如果是字符串，尝试解析为JSON
        try {
          jsonMap = jsonDecode(data) as Map<String, dynamic>?;
        } catch (e) {
          return null;
        }
      } else if (data is Map) {
        // 如果已经是Map，直接使用
        jsonMap = data as Map<String, dynamic>?;
      } else {
        return null;
      }
      
      if (jsonMap == null) return null;
      
      // 尝试读取state字段
      if (jsonMap['state'] is int) {
        return jsonMap['state'] as int;
      }
      // 尝试读取code字段
      if (jsonMap['code'] is int) {
        return jsonMap['code'] as int;
      }
      
      // 检查是否是其他类型但可以转换为int
      if (jsonMap['state'] != null) {
        try {
          final intState = int.parse(jsonMap['state'].toString());
          return intState;
        } catch (e) {
          // ignore parsing error
        }
      }
      if (jsonMap['code'] != null) {
        try {
          final intCode = int.parse(jsonMap['code'].toString());
          return intCode;
        } catch (e) {
          // ignore parsing error
        }
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  String? _tryReadMessage(dynamic data) {
    try {
      Map<String, dynamic>? jsonMap;
      
      // 处理不同类型的数据
      if (data is String) {
        // 如果是字符串，尝试解析为JSON
        try {
          jsonMap = jsonDecode(data) as Map<String, dynamic>?;
        } catch (e) {
          return null;
        }
      } else if (data is Map) {
        // 如果已经是Map，直接使用
        jsonMap = data as Map<String, dynamic>?;
      } else {
        return null;
      }
      
      if (jsonMap == null) return null;
      
      // 尝试读取message字段
      if (jsonMap['message'] is String) {
        return jsonMap['message'] as String;
      }
      // 尝试读取msg字段
      if (jsonMap['msg'] is String) {
        return jsonMap['msg'] as String;
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _onLoginExpired(String message) async {
    eventBus.emit(LoginExpiredEvent(message: message));
  }
}
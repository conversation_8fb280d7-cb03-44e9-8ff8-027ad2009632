import 'dart:io';
import 'package:dlyz_flutter/services/fast_login_http_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../model/login_info.dart';
import '../pages/privacy/privacy_manager.dart';
import '../services/login_api_service.dart';
import '../model/user.dart';
import 'user_provider.dart';
import '../manager/channel_manager.dart';
import '../manager/game_data_manager.dart';
import '../services/app_route_manager.dart';
import '../model/auth_app_item.dart';
import '../config/app_config.dart';
import '../pages/login/fast_login_confirm_page.dart';

/// 微信登录结果状态
enum WeChatLoginResult {
  none,      // 无结果/初始状态
  success,   // 登录成功
  timeout,   // 登录超时
}

/// 登录流程状态管理
class LoginStateProvider extends ChangeNotifier {
  /// 测试需要才打开,todo 在测试配置中心打开
  static const isTestShanYan = false;

  static const _wechatRetrySize = 10;
  static const _wechatSuccessStatus = 1;
  static const _wechatWaitStatus = 2;
  static const _wechatTimeoutStatus = 3;

  LoginState _currentState = LoginState.initial;
  String _currentPhoneNumber = '';
  String _currentNonce = '';
  bool _isLoading = false;
  int _resendCountdown = 0;
  bool _isFastLoginInProgress = false;
  bool _userChoseAccountLogin = false; // 标记用户是否主动选择了账号登录
  List<AuthAppItem> _authAppList = [];
  
  // 当前正在进行微信登录的trace_id和nonce
  String _currentAuthTraceId = '';
  String _currentAuthNonce = '';
  
  // 用户协议同意状态
  bool _agreementAccepted = false;

  // 微信登录状态管理
  bool _weChatLoginState = false;
  int _statusRequestCount = 0;
  DateTime? _lastStatusRequestTime;

  // 微信登录结果状态
  WeChatLoginResult _weChatLoginResult = WeChatLoginResult.none;

  // Getters
  LoginState get currentState => _currentState;

  String get currentPhoneNumber => _currentPhoneNumber;

  String get currentNonce => _currentNonce;

  bool get isLoading => _isLoading;

  int get resendCountdown => _resendCountdown;

  bool get isFastLoginInProgress => _isFastLoginInProgress;

  List<AuthAppItem> get authAppList => _authAppList;

  bool get weChatLoginState => _weChatLoginState;

  WeChatLoginResult get weChatLoginResult => _weChatLoginResult;

  bool get agreementAccepted => _agreementAccepted;

  /// Initialize the provider (alias for initializeLoginFlow)
  Future<void> initialize([BuildContext? context]) async {
    if (context != null) {
      await initializeLoginFlow(context);
    }
  }

  Future<void> initializeLoginFlow(BuildContext context) async {
    _setState(LoginState.loading);
    try {
      // 初始化用户数据，但默认展示手机号登录
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.initialize();
      // 获取授权应用列表
      await _loadAuthAppList();
      // 需求：优先显示手机登录
      go(LoginState.phoneLogin);
      // 后台尝试闪验（仅Android且会话内仅尝试一次）
      // 不阻塞UI，失败自动回落
      // ignore: discarded_futures
      tryFastLogin(context);
    } catch (e) {
      _showErrorToast('初始化失败: ${e.toString()}');
    }
  }

  /// 加载/刷新授权应用列表
  Future<void> _loadAuthAppList() async {
    try {
      final api = LoginApiService();
      final resp = await api.getAuthAppList();
      if (resp.success && resp.data != null) {
        _authAppList = resp.data!.list;
        debugPrint('获取授权应用列表成功，共${_authAppList.length}个应用');
        notifyListeners();
      } else {
        debugPrint('获取授权应用列表失败: ${resp.message}');
        // 失败时使用空列表，不影响登录流程
        _authAppList = [];
      }
    } catch (e, stackTrace) {
      debugPrint('获取授权应用列表异常: $e');
      debugPrint('堆栈信息: $stackTrace');
      // 异常时使用空列表，不影响登录流程
      _authAppList = [];
    }
  }

  // 统一导航入口封装
  void switchToAccountLogin() {
    _userChoseAccountLogin = true; // 设置标志，表示用户主动选择了账号登录
    go(LoginState.accountLogin);
  }

  void switchToPhoneLogin() => go(LoginState.phoneLogin);

  void switchToPhonePasswordLogin() => go(LoginState.phonePasswordLogin);

  void switchToVerifyCode(String phoneNumber) {
    _currentPhoneNumber = phoneNumber;
    sendVerificationCode(phoneNumber);
  }

  /// 导航到下一个登录状态
  void go(LoginState next) {
    _setState(next);
  }


  Future<bool> performAccountLogin({required BuildContext context, required String account, required String password}) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.loginWithPassword(username: account, password: password);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final loginInfoWithTime = resp.data!.copyWith(lastLoginAt: DateTime.now());
        final user = User.fromLoginInfo(loginInfoWithTime);
        await userProvider.loginSuccess(user: user);
        // 登录成功后获取用户详细信息
        await userProvider.fetchCurrentUserDetail();
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> performMobilePasswordLogin({
    required BuildContext context,
    required String mobile,
    required String password,
  }) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.loginWithMobilePassword(mobile: mobile, password: password);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final loginInfoWithTime = resp.data!.copyWith(lastLoginAt: DateTime.now(), recordPhone: mobile);
        final user = User.fromLoginInfo(loginInfoWithTime);
        await userProvider.loginSuccess(user: user);
        // 登录成功后获取用户详细信息
        await userProvider.fetchCurrentUserDetail();
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> sendVerificationCode(String phoneNumber) async {
    _setLoading(true);
    try {
      final api = LoginApiService();
      final resp = await api.sendCode(phone: phoneNumber);
      
      if (resp.success && resp.data != null) {
        _currentPhoneNumber = phoneNumber;
        // 保存nonce用于后续验证码校验
        _currentNonce = resp.data!.nonce;
        _startResendCountdown();
        return true;
      } else {
        _showErrorToast(resp.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('发送验证码失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> verifyPhoneAndLogin({required String phoneNumber, required String verificationCode, BuildContext? context}) async {
    _setLoading(true);
    try {
      // 首先验证验证码
      final api = LoginApiService();
      final checkResult = await api.checkCode(phone: phoneNumber, code: verificationCode, nonce: _currentNonce);
      if (checkResult.success && checkResult.data != null) {
        // 验证码正确，使用票据登录
        final loginResult = await api.loginWithMobileTicket(ticket: checkResult.data!.ticket, nonce: _currentNonce);

        if (loginResult.success && loginResult.data != null) {
          if (context != null) {
            final userProvider = Provider.of<UserProvider>(context, listen: false);
            final loginInfoWithTime = loginResult.data!.copyWith(lastLoginAt: DateTime.now(), recordPhone: phoneNumber);
            final user = User.fromLoginInfo(loginInfoWithTime);
            await userProvider.loginSuccess(user: user);
            // 登录成功后获取用户详细信息
            await userProvider.fetchCurrentUserDetail();
          }
          return true;
        } else {
          _showErrorToast(loginResult.message);
          return false;
        }
      } else {
        _showErrorToast(checkResult.message);
        return false;
      }
    } catch (e) {
      _showErrorToast('登录失败: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }


  /// 设置微信登录状态
  void _setWeChatLoginState(bool state, [WeChatLoginResult? result]) {
    _weChatLoginState = state;
    if (!state) {
      // 清除状态时重置计数器和清空参数
      _statusRequestCount = 0;
      _lastStatusRequestTime = null;
      clearCurrentAuthParams();
      // 设置登录结果
      if (result != null) {
        _weChatLoginResult = result;
      }
    } else {
      // 开始登录时重置结果状态
      _weChatLoginResult = WeChatLoginResult.none;
    }
    notifyListeners();
  }

  /// 设置当前正在进行的微信登录的trace_id和nonce
  void setCurrentAuthParams(String traceId, String nonce) {
    _currentAuthTraceId = traceId;
    _currentAuthNonce = nonce;
    debugPrint('设置当前微信登录参数: trace_id=$traceId, nonce=$nonce');
  }

  /// 清空当前微信登录参数
  void clearCurrentAuthParams() {
    _currentAuthTraceId = '';
    _currentAuthNonce = '';
    debugPrint('清空当前微信登录参数');
  }

  /// 启动微信登录状态监听（供外部调用）
  void startWeChatLoginMonitoring() {
    _setWeChatLoginState(true);
  }

  /// 停止微信登录状态监听（供外部调用）
  void stopWeChatLoginMonitoring() {
    _setWeChatLoginState(false); // 主动停止，不设置具体结果
    // 停止监听时清空参数
    clearCurrentAuthParams();
  }

  /// 检查授权应用状态（带防抖和重试逻辑）
  Future<void> checkAuthAppStatusIfNeeded([BuildContext? context]) async {
    if (!_weChatLoginState) return;
    
    // 使用当前存储的trace_id和nonce（由点击具体应用时设置）
    if (_currentAuthTraceId.isEmpty || _currentAuthNonce.isEmpty) {
      debugPrint('checkAuthAppStatusIfNeeded: 没有可用的trace_id和nonce，跳过检查');
      return;
    }
    
    // 调用带参数的检查方法
    await checkAuthAppStatusWithParams(_currentAuthTraceId, _currentAuthNonce, context);
  }

  /// 检查授权应用状态（新流程：使用具体的trace_id和nonce）
  Future<void> checkAuthAppStatusWithParams(String traceId, String nonce, [BuildContext? context]) async {
    if (!_weChatLoginState) return;

    final now = DateTime.now();

    // 防抖：1s内只能有一次请求
    if (_lastStatusRequestTime != null && now.difference(_lastStatusRequestTime!).inMilliseconds < 1000) {
      return;
    }

    _lastStatusRequestTime = now;

    try {
      final api = LoginApiService();
      final resp = await api.getAuthAppStatus(
        traceId: traceId,
        nonce: nonce,
      );

      if (resp.success && resp.data != null) {
        final status = resp.data!.status;

        switch (status) {
          case _wechatSuccessStatus:
            // 登录成功
            debugPrint('微信登录状态检查：登录成功');
            _setWeChatLoginState(false, WeChatLoginResult.success);
            // 处理登录成功逻辑 - 使用返回的登录信息
            if (context != null) {
              await _handleWeChatLoginSuccess(context, resp.data!.loginInfo);
            } else {
              debugPrint('微信登录成功，但缺少context，无法完成用户缓存');
              _showSuccessToast('微信登录成功');
            }
            break;

          case _wechatWaitStatus:
            // 继续等待，增加计数
            _statusRequestCount++;
            debugPrint('微信登录状态检查：等待中，第$_statusRequestCount次');

            // 达到10次后转为状态3处理
            if (_statusRequestCount >= _wechatRetrySize) {
              debugPrint('微信登录状态检查：达到最大重试次数，刷新数据');
              _showErrorToast('微信登录请求已过期，请重试');
              _setWeChatLoginState(false, WeChatLoginResult.timeout);
            }
            break;

          case _wechatTimeoutStatus:
            // 调用刷新数据
            debugPrint('微信登录状态检查：需要刷新数据');
            _showErrorToast('微信登录请求已过期，请重试');
            _setWeChatLoginState(false, WeChatLoginResult.timeout);
            break;

          default:
            debugPrint('微信登录状态检查：未知状态 $status');
        }
      }
    } catch (e) {
      debugPrint('检查授权应用状态失败: $e');
    }
  }

  /// 处理微信登录成功
  Future<void> _handleWeChatLoginSuccess(BuildContext context, LoginInfo loginInfo) async {
    try {
      debugPrint('微信登录成功，正在处理用户信息...');

      // 使用返回的登录信息，如果没有则使用模拟数据
      final actualLoginInfo = loginInfo.copyWith(lastLoginAt: DateTime.now());
      
      // 创建用户对象并缓存登录状态
      final user = User.fromLoginInfo(actualLoginInfo);
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.loginSuccess(user: user);
      // 登录成功后获取用户详细信息
      await userProvider.fetchCurrentUserDetail();

      _showSuccessToast('微信登录成功');
      
      // 登录成功后导航到主页面
      if (context.mounted) {
        AppRouteManager.navigateAfterLogin(context);
      }
      
      debugPrint('微信登录成功，用户信息: ${actualLoginInfo.muname}');
    } catch (e) {
      debugPrint('处理微信登录成功失败: $e');
      _showErrorToast('登录处理失败: $e');
    }
  }

  /// 显示成功Toast
  void _showSuccessToast(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }

  Future<void> resendVerificationCode() async {
    if (_resendCountdown > 0) return;
    await sendVerificationCode(_currentPhoneNumber);
  }

  void resetLoginFlow() {
    _currentState = LoginState.initial;
    _currentPhoneNumber = '';
    _currentNonce = '';
    _isLoading = false;
    _resendCountdown = 0;
    _userChoseAccountLogin = false; // 重置标志
    _setWeChatLoginState(false); // 重置时清除状态
    notifyListeners();
  }

  /// 设置用户协议同意状态
  void setAgreementAccepted(bool accepted) {
    _agreementAccepted = accepted;
    notifyListeners();
  }

  // state helpers
  void _setState(LoginState newState) {
    _currentState = newState;
    notifyListeners();
  }

  void _showErrorToast(String message) {
    _isLoading = false;
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.CENTER,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _startResendCountdown() {
    _resendCountdown = 60;
    _countdownTimer();
  }

  void _countdownTimer() {
    if (_resendCountdown > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        _resendCountdown--;
        notifyListeners();
        _countdownTimer();
      });
    }
  }

  /// 处理微信小游戏选择逻辑
  /// [tgid] 游戏标识
  /// [miniProgramId] 小程序ID  
  /// [context] 上下文，用于显示错误提示
  Future<void> handleWeChatGameSelection({
    required String tgid,
    required String miniProgramId,
    required BuildContext context,
  }) async {
    // 启动微信登录状态监听
    startWeChatLoginMonitoring();
    
    try {
      // 首先调用 applyAuthApp 获取 nonce 和 trace_id
      final api = LoginApiService();
      final applyResult = await api.applyAuthApp(tgid: tgid);
      
      if (!applyResult.success || applyResult.data == null) {
        stopWeChatLoginMonitoring();
        if (context.mounted) {
          _showErrorToast('获取授权信息失败: ${applyResult.message}');
        }
        return;
      }
      
      final authData = applyResult.data!;
      
      // 设置当前微信登录参数，供后续可见性检查使用
      setCurrentAuthParams(authData.traceId, authData.nonce);
      
      // 根据不同类型的配置信息跳转
      if (miniProgramId.isNotEmpty) {
        // 使用小程序ID调用getSkipAppletInfo接口
        final flagId = int.tryParse(miniProgramId);
        if (flagId != null) {
          await GameDataManager().requestJumpMiniProgram(
            flagId,
            extra: '&cq=${Uri.encodeComponent('appid=${AppConfig.appId}&app_pid=${AppConfig.pid}&app_gid=${AppConfig.gid}&nonce=${authData.nonce}&trace_id=${authData.traceId}')}',
          );
          // 跳转成功，保持微信登录状态监听
        } else {
          // flagId解析失败，停止监听
          stopWeChatLoginMonitoring();
          if (context.mounted) {
            _showErrorToast('小程序ID格式错误');
          }
        }
      } else {
        // 缺少跳转信息，停止监听
        stopWeChatLoginMonitoring();
        if (context.mounted) {
          _showErrorToast('缺少跳转信息');
        }
      }
    } catch (e) {
      // 发生异常，停止监听
      stopWeChatLoginMonitoring();
      if (context.mounted) {
        _showErrorToast('跳转失败: $e');
      }
    }
  }

  /// 导航到闪验确认页面
  Future<void> tryFastLogin(BuildContext context) async {

    if (_isFastLoginInProgress) {
      return;
    }

    if(kDebugMode && isTestShanYan) {
      // 导航到确认页面，传递回调使用上层context
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => FastLoginConfirmPage(
            onContinue: () => proceedWithFastLogin(context),
          ),
        ),
      );
    } else {
      proceedWithFastLogin(context);
    }

  }

  /// 执行原生闪验登录的实际逻辑
  /// 不阻塞UI，内部自行处理回退与成功导航。
  Future<void> proceedWithFastLogin(BuildContext context) async {
    if (_isFastLoginInProgress) {
      return;
    }

    _isFastLoginInProgress = true;
    try {
      // 拉取配置（失败直接回落手机号登录，不进入错误重试页）
      Map<String, dynamic> config;
      try {
        // 可以走线上配置闪验，用于调试， Todo：后续去掉
        // config = await FastLoginHttpUtil.requestFastConfig();

        // 走测试环境配置闪验
        final response = await LoginApiService().requestFastConfig();
        if (!response.isSuccess || response.data == null) {
          go(LoginState.phoneLogin);
          return;
        }
        config = response.data!;
      } catch (e) {
        go(LoginState.phoneLogin);
        return;
      }

      // 设置协议地址（可选：按产品实际替换）
      // 将授权应用列表添加到配置中
      final Map<String, dynamic> configWithAuthApps = Map.from(config);
      configWithAuthApps['authAppList'] = _authAppList.map((app) => app.toJson()).toList();

      // 获取动态协议URL
      final userUrl = await PrivacyManager.userAgreementUrl;
      final privacyUrl = await PrivacyManager.privacyPolicyUrl;

      final bool configOk = await ChannelManager().setFastLoginConfig(
        configWithAuthApps,
        userAgreementUrl: userUrl.isNotEmpty
            ? '$userUrl&isAgree=true&env=dl'
            : 'https://example.com/user-agreement',
        privacyPolicyUrl: privacyUrl.isNotEmpty
            ? '$privacyUrl&isAgree=true&env=dl'
            : 'https://example.com/privacy-policy',
      );
      if (!configOk) {
        go(LoginState.phoneLogin);
        return;
      }

      // 环境检测
      final bool envOk = await ChannelManager().checkFastLoginEnvironment();
      if (!envOk) {
        go(LoginState.phoneLogin);
        return;
      }

      if (Platform.isAndroid) {
        // 初始化SDK
        final bool initOk = await ChannelManager().initializeFastLogin();
        if (!initOk) {
          go(LoginState.phoneLogin);
          return;
        }
      }

      // 监听原生UI事件
      ChannelManager().setFastLoginUIEventListener((String method, Map<String, dynamic> arguments) {
        if (Platform.isIOS) {
          _isFastLoginInProgress = false;
        }
        switch (method) {
          case 'close_clicked':
          case 'back_clicked':
            switchToPhoneLogin();
            break;
          case 'wechat_game_selected':
            // 处理微信游戏选择事件
            final String tgid = arguments['tgid'] ?? '';
            final String miniProgramId = arguments['mini_program_id'] ?? '';
            if (tgid.isNotEmpty && miniProgramId.isNotEmpty) {
              // 调用抽取的方法处理微信游戏选择逻辑
              handleWeChatGameSelection(
                tgid: tgid,
                miniProgramId: miniProgramId,
                context: context,
              );
            } else {
              debugPrint('微信游戏选择参数不完整: tgid=$tgid, miniProgramId=$miniProgramId');
            }
            break;
          case 'account_login_clicked':
            switchToAccountLogin();
            break;
          case 'other_phone_clicked':
            switchToPhoneLogin();
            break;
          case 'show_protocol_click_tip':
            break;
          case 'forget_password_clicked':
            break;
          default:
            // 未知事件：不处理
            debugPrint('未知的闪验UI事件: $method');
            break;
        }
      });

      // 不切换至 fastLogin，保持当前手机号登录界面，避免Loading视觉

      // 拉起并等待结果
      final Map<String, dynamic> result = await ChannelManager().doFastLogin();
      final success = result['success'] == true;
      final token = result['token'] as String?;

      if (!success || token == null || token.isEmpty) {
        _showErrorToast('闪验登录失败');
        return;
      }

      // 后端换票据
      final api = LoginApiService();
      final resp = await api.loginWithFastToken(token: token);
      if (resp.success && resp.data != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final loginInfoWithTime = resp.data!.copyWith(lastLoginAt: DateTime.now());
        final user = User.fromLoginInfo(loginInfoWithTime);
        await userProvider.loginSuccess(user: user);
        // 登录成功后获取用户详细信息
        await userProvider.fetchCurrentUserDetail();
        if (context.mounted) {
          AppRouteManager.navigateAfterLogin(context);
        }
        return;
      }

      _showErrorToast(resp.message);
    } on FastLoginCancelException {
      // 用户主动取消 → 回落手机号，但如果用户选择了账号登录则保持账号登录状态
      if (!_userChoseAccountLogin) {
        switchToPhoneLogin();
      }
    } catch (e) {
      print('闪验异常: ${e.toString()}');
      if (!_userChoseAccountLogin) {
        switchToPhoneLogin();
      }
    } finally {
      _isFastLoginInProgress = false;
      ChannelManager().setFastLoginUIEventListener(null);
    }
  }
}

enum LoginState { initial, loading, accountLogin, phoneLogin, phonePasswordLogin, wechatLogin, fastLogin, success, error }

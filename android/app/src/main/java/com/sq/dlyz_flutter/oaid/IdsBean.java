package com.sq.dlyz_flutter.oaid;

import org.json.JSONException;
import org.json.JSONObject;

public class IdsBean {

    public String oaid;

    public String aaid;

    public String vaid;

    public IdsBean(String aaid, String oaid, String vaid) {
        this.aaid = aaid;
        this.oaid = oaid;
        this.vaid = vaid;
    }

    public String getAaid() {
        return aaid;
    }

    public String getOaid() {
        return oaid;
    }

    public String getVaid() {
        return vaid;
    }

    @Override
    public String toString() {
        JSONObject idsObj = new JSONObject();
        try {
            idsObj.put("aaid", aaid);
            idsObj.put("oaid", oaid);
            idsObj.put("vaid", vaid);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return idsObj.toString();
    }
}
package com.sq.dlyz_flutter.oaid;

import android.content.Context;
import android.os.Build;
import android.util.Log;
import com.bun.miitmdid.core.ErrorCode;
import com.bun.miitmdid.core.JLibrary;
import com.bun.miitmdid.core.MdidSdkHelper;
import com.bun.supplier.IIdentifierListener;
import com.bun.supplier.IdSupplier;

public class IdsHelper implements IIdentifierListener {

    private static final String TAG = "IdsHelper";

    protected CallBack mCallBack;

    public IdsHelper() {
    }

    public void getId(Context cxt, CallBack callBack) {
        try {
            mCallBack = callBack;
            int nres = MdidSdkHelper.InitSdk(cxt, true, this);

            Log.i(TAG, "getId: " + nres);
            if (nres == ErrorCode.INIT_ERROR_DEVICE_NOSUPPORT) {//不支持的设备
                Log.i(getClass().getSimpleName(), "return: ErrorCode.INIT_ERROR_DEVICE_NOSUPPORT");
            } else if (nres == ErrorCode.INIT_ERROR_LOAD_CONFIGFILE) {//加载配置文件出错
                Log.i(getClass().getSimpleName(), "return: ErrorCode.INIT_ERROR_LOAD_CONFIGFILE");
            } else if (nres == ErrorCode.INIT_ERROR_MANUFACTURER_NOSUPPORT) {//不支持的设备厂商
                Log.i(getClass().getSimpleName(), "return: ErrorCode.INIT_ERROR_MANUFACTURER_NOSUPPORT");
            } else if (nres == ErrorCode.INIT_ERROR_RESULT_DELAY) {//获取接口是异步的，结果会在回调中返回，回调执行的回调可能在工作线程
                Log.i(getClass().getSimpleName(), "return: ErrorCode.INIT_ERROR_RESULT_DELAY");
            } else if (nres == ErrorCode.INIT_HELPER_CALL_ERROR) {//反射调用出错
                Log.i(getClass().getSimpleName(), "return: ErrorCode.INIT_HELPER_CALL_ERROR");
            } else {
                Log.i(getClass().getSimpleName(), "unknow: " + nres);
            }
            if (mCallBack != null) {
                mCallBack.OnResultCode(nres, "");
            }
        } catch (Throwable e) {
            Log.i(TAG, "getId error " );
            e.printStackTrace();
            if (mCallBack != null) {
                mCallBack.OnResultCode(1008110, e.toString());
            }
        }
    }

    @Override
    public void OnSupport(boolean isSupport, IdSupplier _supplier) {
        Log.i(TAG, "OnSupport: " + isSupport);
        if (!isSupport || _supplier == null || !_supplier.isSupported()) {
            if (mCallBack != null) {
                mCallBack.OnResultCode(ErrorCode.INIT_ERROR_DEVICE_NOSUPPORT, "设备不支持或供应商不可用");
            }
        } else {
            IdsBean idsBean = new IdsBean(_supplier.getAAID(), _supplier.getOAID(), _supplier.getVAID());
            if (mCallBack != null) {
                mCallBack.OnOAIDValid(idsBean);
            }
        }

    }


    public interface CallBack {
        void OnOAIDValid(IdsBean ids);

        void OnResultCode(int code, String msg);
    }


    public static void attachBaseContext(Context base) {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
            Log.d(TAG, "android版本6以下，不执行oaid获取");
            return;
        }
        try {
            JLibrary.InitEntry(base);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}